'use client';

import dynamic from 'next/dynamic';
import { ComponentType, forwardRef } from 'react';
import { ClarityReplayProps, ClarityReplayRef } from './clarity-replay';
import ClarityErrorBoundary from './ClarityErrorBoundary';

// Loading component for dynamic import
const LoadingComponent = () => (
	<div className="flex items-center justify-center h-96 bg-gray-50 rounded-lg border border-gray-200">
		<div className="text-center">
			<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
			<p className="text-gray-600">Loading replay component...</p>
			<p className="text-xs text-gray-500 mt-2">Initializing Clarity visualizer...</p>
		</div>
	</div>
);

// Error fallback for dynamic import failures
const ErrorFallback = ({ error, retry }: { error?: Error; retry?: () => void }) => (
	<div className="flex flex-col items-center justify-center h-96 bg-red-50 border border-red-200 rounded-lg p-6">
		<div className="text-center max-w-md">
			<div className="text-red-600 mb-4">
				<svg className="w-12 h-12 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path
						strokeLinecap="round"
						strokeLinejoin="round"
						strokeWidth={2}
						d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 19.5c-.77.833.192 2.5 1.732 2.5z"
					/>
				</svg>
			</div>

			<h3 className="text-lg font-semibold text-red-800 mb-2">Failed to Load Replay Component</h3>

			<p className="text-red-600 mb-4 text-sm">
				{error?.message ||
					'The replay component could not be loaded. This might be due to a network issue or browser compatibility.'}
			</p>

			{retry && (
				<button
					onClick={retry}
					className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors"
				>
					Try Again
				</button>
			)}
		</div>
	</div>
);

// Dynamically import the ClarityReplay component with no SSR
const DynamicClarityReplay = dynamic(() => import('./clarity-replay').then((mod) => ({ default: mod.default })), {
	ssr: false,
	loading: LoadingComponent
}) as ComponentType<ClarityReplayProps>;

/**
 * Next.js optimized wrapper for ClarityReplay component
 * Handles SSR, dynamic imports, and provides enhanced error boundaries
 */
const ClarityReplayWrapper = forwardRef<
	ClarityReplayRef,
	ClarityReplayProps & {
		enableErrorBoundary?: boolean;
		errorFallback?: React.ComponentType<{ error?: Error; retry?: () => void }>;
		loadingComponent?: React.ComponentType;
	}
>(
	(
		{
			enableErrorBoundary = true,
			errorFallback: CustomErrorFallback,
			loadingComponent: CustomLoadingComponent,
			...props
		},
		ref
	) => {
		// Custom error handler for the error boundary
		const handleError = (error: Error, errorInfo: any) => {
			console.error('ClarityReplayWrapper error boundary:', error, errorInfo);

			// You could send this to an error reporting service
			// Example: reportError(error, { context: 'ClarityReplay', ...errorInfo });
		};

		const ReplayComponent = <DynamicClarityReplay ref={ref} {...props} />;

		// Wrap with error boundary if enabled
		if (enableErrorBoundary) {
			return (
				<ClarityErrorBoundary
					onError={handleError}
					fallback={CustomErrorFallback ? <CustomErrorFallback /> : undefined}
				>
					{ReplayComponent}
				</ClarityErrorBoundary>
			);
		}

		return ReplayComponent;
	}
);

ClarityReplayWrapper.displayName = 'ClarityReplayWrapper';

export default ClarityReplayWrapper;

// Re-export types for convenience
export type { ClarityReplayProps, ClarityReplayRef } from './clarity-replay';
