# Filtered Sessions API Endpoint

This document describes the new GET endpoint for retrieving tracking sessions filtered by organization ID, employee ID(s), and date range.

## Endpoint Details

**Method:** GET
**Path:** `/api/tracking`
**Authentication:** <PERSON><PERSON>ken required

## Required Headers

| Header            | Type   | Description                              |
| ----------------- | ------ | ---------------------------------------- |
| `Accept`          | string | Must be `application/json`               |
| `Authorization`   | string | Bearer token in format: `Bearer <token>` |
| `tenant-id`       | string | Tenant identifier                        |
| `organization-id` | string | Organization identifier                  |

## Query Parameters

| Parameter     | Type   | Required | Description                                                                                            |
| ------------- | ------ | -------- | ------------------------------------------------------------------------------------------------------ |
| `from`        | string | Yes      | Start date in ISO 8601 format (e.g., "2024-01-01T00:00:00.000Z")                                       |
| `to`          | string | Yes      | End date in ISO 8601 format (e.g., "2024-01-31T23:59:59.999Z")                                         |
| `employeeIds` | string | No       | JSON array of employee IDs (e.g., `["emp1", "emp2"]`). If omitted, returns sessions for all employees. |

## Example Requests

### Get Sessions for Specific Employees

```bash
curl -X GET "http://localhost:3000/api/tracking?from=2024-01-01T00:00:00.000Z&to=2024-01-31T23:59:59.999Z&employeeIds=[\"emp-123\",\"emp-456\"]" \
  -H "Accept: application/json" \
  -H "Authorization: Bearer your-token-here" \
  -H "tenant-id: tenant-789" \
  -H "organization-id: org-456"
```

### Get Sessions for All Employees

```bash
curl -X GET "http://localhost:3000/api/tracking?from=2024-01-01T00:00:00.000Z&to=2024-01-31T23:59:59.999Z" \
  -H "Accept: application/json" \
  -H "Authorization: Bearer your-token-here" \
  -H "tenant-id: tenant-789" \
  -H "organization-id: org-456"
```

## Response Format

### Success Response (200)

```json
{
	"success": true,
	"data": [
		{
			"sessionId": "session-123",
			"payloads": [
				{
					"payload": "encoded-payload-data",
					"timestamp": "2024-01-15T10:00:00.000Z"
				}
			],
			"createdAt": "2024-01-15T09:00:00.000Z",
			"updatedAt": "2024-01-15T10:00:00.000Z",
			"employeeId": "emp-123",
			"organizationId": "org-456",
			"tenantId": "tenant-789"
		}
	]
}
```

### Error Response (4xx/5xx)

```json
{
	"success": false,
	"error": "Error message",
	"details": "Detailed error description"
}
```

## Error Codes

| Status Code | Error                                   | Description                                             |
| ----------- | --------------------------------------- | ------------------------------------------------------- |
| 400         | Missing required header                 | One of the required headers is missing                  |
| 400         | Missing required query parameter        | One of the required query parameters is missing         |
| 400         | Invalid date format                     | Date parameters are not in valid ISO 8601 format        |
| 400         | Invalid date range                      | From date is not earlier than to date                   |
| 400         | Invalid employeeIds format              | employeeIds parameter is not a valid JSON array         |
| 401         | Missing or invalid Authorization header | Authorization header is missing or not in Bearer format |
| 500         | Internal server error                   | Unexpected server error occurred                        |

## TypeScript Usage

### Using the TrackingApiClient

#### Get Sessions for Specific Employees

```typescript
import { trackingApiClient } from './api/tracking-client';
import { isSuccessResponse } from './types/api-types';

const response = await trackingApiClient.getFilteredSessions(
	'2024-01-01T00:00:00.000Z', // from
	'2024-01-31T23:59:59.999Z', // to
	['emp-123', 'emp-456'], // employeeIds
	'org-456', // organizationId
	'tenant-789', // tenantId
	'your-bearer-token' // bearerToken
);

if (isSuccessResponse(response)) {
	console.log('Sessions:', response.data);
} else {
	console.error('Error:', response.error);
}
```

#### Get Sessions for All Employees

```typescript
import { trackingApiClient } from './api/tracking-client';
import { isSuccessResponse } from './types/api-types';

const response = await trackingApiClient.getFilteredSessions(
	'2024-01-01T00:00:00.000Z', // from
	'2024-01-31T23:59:59.999Z', // to
	null, // employeeIds (null for all employees)
	'org-456', // organizationId
	'tenant-789', // tenantId
	'your-bearer-token' // bearerToken
);

if (isSuccessResponse(response)) {
	console.log('Sessions:', response.data);
} else {
	console.error('Error:', response.error);
}
```

### Manual Fetch

```typescript
const queryParams = new URLSearchParams({
	from: '2024-01-01T00:00:00.000Z',
	to: '2024-01-31T23:59:59.999Z',
	employeeIds: JSON.stringify(['emp-123', 'emp-456'])
});

const response = await fetch(`/api/tracking?${queryParams}`, {
	method: 'GET',
	headers: {
		Accept: 'application/json',
		Authorization: 'Bearer your-token-here',
		'tenant-id': 'tenant-789',
		'organization-id': 'org-456'
	}
});

const data = await response.json();
```

## Filtering Logic

The endpoint filters sessions based on the following criteria:

1. **Organization Match**: Session's `organizationId` must match the `organization-id` header
2. **Tenant Match**: Session's `tenantId` must match the `tenant-id` header
3. **Employee Match**: If `employeeIds` parameter is provided, session's `employeeId` must be included in the array. If omitted, sessions for all employees are returned.
4. **Date Range**: Session's `updatedAt` timestamp must fall within the specified date range (inclusive)

## Implementation Notes

- The endpoint uses the session's `updatedAt` timestamp for date range filtering
- All date comparisons are performed using JavaScript Date objects
- The `employeeIds` parameter must be a valid JSON array string
- Empty results return a 200 status with an empty array
- CORS headers are included to support cross-origin requests

## Testing

A comprehensive test suite is available at:
`packages/toolkit/examples/next/src/app/api/tracking/__tests__/route.test.ts`

An interactive example component is available at:
`packages/toolkit/examples/next/src/app/replay/examples/filtered-sessions-example.tsx`

## Related Files

- **Route Implementation**: `packages/toolkit/examples/next/src/app/api/tracking/route.ts`
- **Type Definitions**: `packages/toolkit/examples/next/src/app/replay/types/api-types.ts`
- **API Client**: `packages/toolkit/examples/next/src/app/replay/api/tracking-client.ts`
- **Example Component**: `packages/toolkit/examples/next/src/app/replay/examples/filtered-sessions-example.tsx`
- **Tests**: `packages/toolkit/examples/next/src/app/api/tracking/__tests__/route.test.ts`
